package markets

// ProductSearchParams represents the search parameters for products
type ProductSearchParams struct {
	Query         string `json:"query"`
	CategoryID    *int   `json:"category_id,omitempty"`
	SubcategoryID *int   `json:"subcategory_id,omitempty"`
	MarketID      *int   `json:"market_id,omitempty"`
	SubmarketID   *int   `json:"submarket_id,omitempty"`
	Language      string `json:"language"`
	Limit         int    `json:"limit"`
	Offset        int    `json:"offset"`
}

// ProductSearchResult represents a product search result
type ProductSearchResult struct {
	ID            int    `json:"id"`
	Name          string `json:"name"`    // This will be either en_name or th_name based on language
	ThName        string `json:"th_name"` // Always return Thai name for reference
	MarketID      int    `json:"market_id"`
	MarketName    string `json:"market_name"`
	MarketNameTh  string `json:"market_name_th"` // Added this field
	SubmarketID   int    `json:"submarket_id"`
	SubmarketName string `json:"submarket_name"`
}
type MarketspaceOption struct {
	ID     int    `json:"id"`
	EnName string `json:"en_name"`
	ThName string `json:"th_name"`
}
type QualityOption struct {
	ID             int    `json:"id"`
	StandardID     int    `json:"standard_id"`
	StandardEnName string `json:"standard_en_name"`
	GradeID        int    `json:"grade_id"`
	GradeEnName    string `json:"grade_en_name"`
}

type DeliveryTermOption struct {
	ID     int    `json:"id"`
	EnName string `json:"en_name"`
	ThName string `json:"th_name"`
}

type PaymentTermOption struct {
	ID     int    `json:"id"`
	EnName string `json:"en_name"`
	ThName string `json:"th_name"`
}

type MarketOption struct {
	ID     int    `json:"id"`
	EnName string `json:"en_name"`
	ThName string `json:"th_name"`
}

type ContractTypeOption struct {
	ID     int    `json:"id"`
	EnName string `json:"en_name"`
	ThName string `json:"th_name"`
}

// MarketDataFilter represents filter parameters for market data queries
type MarketDataFilter struct {
	ProductID      int `json:"product_id"`
	MarketID       int `json:"market_id"`
	MarketspaceID  int `json:"marketspace_id"`
	QualityID      int `json:"quality_id"`
	DeliveryTermID int `json:"delivery_term_id"`
	PaymentTermID  int `json:"payment_term_id"`
	ContractTypeID int `json:"contract_type_id"`
	Days           int `json:"days"`
}

// MarketData represents market data for a specific contract period with 14 structured columns
// Organized into Bid Section (Buy Orders - order_type_id = 1) and Offer Section (Sell Orders - order_type_id = 2)
type MarketData struct {
	// Contract Period (month/year format from contract date)
	ContractPeriod string `json:"contract_period"`

	// Bid Section (Buy Orders - order_type_id = 1)
	BidOpen   float64 `json:"bid_open"`   // Opening bid price for the period
	BidHigh   float64 `json:"bid_high"`   // Highest bid price in the period
	BidLow    float64 `json:"bid_low"`    // Lowest bid price in the period
	BidVolume float64 `json:"bid_volume"` // Total volume of buy orders
	Bid       float64 `json:"bid"`        // Current/latest average bid price
	BidChange float64 `json:"bid_change"` // Absolute price change from previous period

	// Offer Section (Sell Orders - order_type_id = 2)
	OfferOpen   float64 `json:"offer_open"`   // Opening offer price for the period
	OfferHigh   float64 `json:"offer_high"`   // Highest offer price in the period
	OfferLow    float64 `json:"offer_low"`    // Lowest offer price in the period
	OfferVolume float64 `json:"offer_volume"` // Total volume of sell orders
	Offer       float64 `json:"offer"`        // Current/latest average offer price
	OfferChange float64 `json:"offer_change"` // Absolute price change from previous period
	OfferSpread float64 `json:"offer_spread"` // Difference between offer high and low prices
}

// PriceHistoryFilter contains filter parameters for price history queries
type PriceHistoryFilter struct {
	ProductID      int
	Days           int
	MarketID       int
	MarketspaceID  int
	QualityID      int
	DeliveryTermID int
	PaymentTermID  int
	ContractTypeID int
}

// PriceHistoryPoint represents a single point in the price history
type PriceHistoryPoint struct {
	Date        string  `json:"date"`
	BidPrice    float64 `json:"bid_price"`
	OfferPrice  float64 `json:"offer_price"`
	BidVolume   float64 `json:"bid_volume"`
	OfferVolume float64 `json:"offer_volume"`
	MarketCap   float64 `json:"market_cap"`
}

// ProductDetails represents detailed information about a product including market and submarket data
type ProductDetails struct {
	ID              int             `json:"id"`
	EnName          string          `json:"en_name"`
	ThName          string          `json:"th_name"`
	EnDescription   string          `json:"en_description,omitempty"`
	ThDescription   string          `json:"th_description,omitempty"`
	MarketID        int             `json:"market_id"`
	MarketEnName    string          `json:"market_en_name"`
	MarketThName    string          `json:"market_th_name"`
	SubmarketID     *int            `json:"submarket_id,omitempty"`
	SubmarketEnName string          `json:"submarket_en_name,omitempty"`
	SubmarketThName string          `json:"submarket_th_name,omitempty"`
	Qualities       []QualityOption `json:"qualities,omitempty"`
	// New fields for clickable product name functionality
	IsClickable    bool   `json:"is_clickable"`
	ProductPageURL string `json:"product_page_url,omitempty"`
}
