package markets

import (
	"context"
	"database/sql"
	"fmt"
	"log"
)

type Repository interface {
	SearchProducts(ctx context.Context, params ProductSearchParams) ([]ProductSearchResult, error)
	GetProductQualities(ctx context.Context, productID int, lang string) ([]QualityOption, error)
	GetDeliveryTerms(ctx context.Context, isLocal bool) ([]DeliveryTermOption, error)
	GetPaymentTerms(ctx context.Context, isLocal bool) ([]PaymentTermOption, error)
	GetMarkets(ctx context.Context) ([]MarketOption, error)
	GetContractTypes(ctx context.Context) ([]ContractTypeOption, error)
	GetMarketspaces(ctx context.Context) ([]MarketspaceOption, error)
	GetFilteredMarketData(ctx context.Context, filter MarketDataFilter) ([]MarketData, error)
	GetPriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error)
	GetRealtimePriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error)
	GetProductDetails(ctx context.Context, productID int, lang string) (*ProductDetails, error)
}

type repository struct {
	db *sql.DB
}

func NewRepository(db *sql.DB) Repository {
	return &repository{db: db}
}

func (r *repository) SearchProducts(ctx context.Context, params ProductSearchParams) ([]ProductSearchResult, error) {
	query := `
		SELECT
			p.id,
			COALESCE(p.en_name, '') as en_name,
			COALESCE(p.th_name, '') as th_name,
			COALESCE(m.id, 0) as market_id,
			COALESCE(m.en_name, '') as market_name,
			COALESCE(m.th_name, '') as market_name_th
		FROM products p
		LEFT JOIN markets m ON p.market_id = m.id
		WHERE (p.en_name ILIKE $1 OR p.th_name ILIKE $1)
		ORDER BY p.en_name
		LIMIT $2
	`

	searchPattern := "%" + params.Query + "%"
	rows, err := r.db.QueryContext(ctx, query, searchPattern, params.Limit)
	if err != nil {
		return nil, fmt.Errorf("error querying products: %v", err)
	}
	defer rows.Close()

	var results []ProductSearchResult
	for rows.Next() {
		var result ProductSearchResult
		err := rows.Scan(
			&result.ID,
			&result.Name,
			&result.ThName,
			&result.MarketID,
			&result.MarketName,
			&result.MarketNameTh,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning product row: %v", err)
		}
		results = append(results, result)
	}

	return results, nil
}

// Update the scan to match column aliases
func (r *repository) GetProductQualities(ctx context.Context, productID int, lang string) ([]QualityOption, error) {
	query := `
        SELECT q.id, q.standard_id, s.en_name as standard_en_name, q.grade_id, g.en_name as grade_en_name
        FROM qualities q
        LEFT JOIN standards s ON q.standard_id = s.id
        LEFT JOIN grades g ON q.grade_id = g.id
        WHERE q.product_id = $1
        ORDER BY s.en_name ASC
    `

	// Add debug logging
	fmt.Printf("Executing query for product ID: %d\n", productID)

	rows, err := r.db.QueryContext(ctx, query, productID)
	if err != nil {
		return nil, fmt.Errorf("error querying product qualities: %v", err)
	}
	defer rows.Close()

	var qualities []QualityOption
	for rows.Next() {
		var q QualityOption
		err := rows.Scan(
			&q.ID,
			&q.StandardID,
			&q.StandardEnName, // Changed from StandardName
			&q.GradeID,
			&q.GradeEnName, // Changed from GradeName
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning quality row: %v", err)
		}
		qualities = append(qualities, q)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating quality rows: %v", err)
	}

	return qualities, nil
}

func (r *repository) GetDeliveryTerms(ctx context.Context, isLocal bool) ([]DeliveryTermOption, error) {
	marketspaceID := 2 // Default to global
	if isLocal {
		marketspaceID = 1 // Local marketspace
	}

	query := `
        SELECT dt.id, dt.en_name, dt.th_name
        FROM delivery_terms_marketspaces dtm
        INNER JOIN delivery_terms dt ON dtm.delivery_term_id = dt.id
        WHERE dtm.marketspace_id = $1
        ORDER BY dt.en_name ASC
    `

	rows, err := r.db.QueryContext(ctx, query, marketspaceID)
	if err != nil {
		return nil, fmt.Errorf("error querying delivery terms: %v", err)
	}
	defer rows.Close()

	var terms []DeliveryTermOption
	for rows.Next() {
		var term DeliveryTermOption
		err := rows.Scan(
			&term.ID,
			&term.EnName,
			&term.ThName,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning delivery term row: %v", err)
		}
		terms = append(terms, term)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating delivery term rows: %v", err)
	}

	return terms, nil
}

func (r *repository) GetPaymentTerms(ctx context.Context, isLocal bool) ([]PaymentTermOption, error) {
	marketspaceID := 2 // Default to global
	if isLocal {
		marketspaceID = 1 // Local marketspace
	}

	query := `
        SELECT pt.id, pt.en_name, pt.th_name
        FROM payment_terms_marketspaces ptm
        INNER JOIN payment_terms pt ON ptm.payment_term_id = pt.id
        WHERE ptm.marketspace_id = $1
        ORDER BY pt.en_name ASC
    `

	rows, err := r.db.QueryContext(ctx, query, marketspaceID)
	if err != nil {
		return nil, fmt.Errorf("error querying payment terms: %v", err)
	}
	defer rows.Close()

	var terms []PaymentTermOption
	for rows.Next() {
		var term PaymentTermOption
		err := rows.Scan(
			&term.ID,
			&term.EnName,
			&term.ThName,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning payment term row: %v", err)
		}
		terms = append(terms, term)
	}

	return terms, nil
}

func (r *repository) GetMarkets(ctx context.Context) ([]MarketOption, error) {
	query := `SELECT id, en_name, th_name FROM markets ORDER BY id ASC`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("error querying markets: %v", err)
	}
	defer rows.Close()

	var markets []MarketOption
	for rows.Next() {
		var market MarketOption
		err := rows.Scan(&market.ID, &market.EnName, &market.ThName)
		if err != nil {
			return nil, fmt.Errorf("error scanning market row: %v", err)
		}
		markets = append(markets, market)
	}
	return markets, nil
}

func (r *repository) GetContractTypes(ctx context.Context) ([]ContractTypeOption, error) {
	query := `SELECT id, en_name, th_name FROM contract_types ORDER BY id ASC`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("error querying contract types: %v", err)
	}
	defer rows.Close()

	var contractTypes []ContractTypeOption
	for rows.Next() {
		var contractType ContractTypeOption
		err := rows.Scan(&contractType.ID, &contractType.EnName, &contractType.ThName)
		if err != nil {
			return nil, fmt.Errorf("error scanning contract type row: %v", err)
		}
		contractTypes = append(contractTypes, contractType)
	}
	return contractTypes, nil
}

func (r *repository) GetMarketspaces(ctx context.Context) ([]MarketspaceOption, error) {
	query := `SELECT id, en_name, th_name FROM marketspaces ORDER BY id ASC`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("error querying marketspaces: %v", err)
	}
	defer rows.Close()

	var marketspaces []MarketspaceOption
	for rows.Next() {
		var marketspace MarketspaceOption
		err := rows.Scan(&marketspace.ID, &marketspace.EnName, &marketspace.ThName)
		if err != nil {
			return nil, fmt.Errorf("error scanning marketspace row: %v", err)
		}
		marketspaces = append(marketspaces, marketspace)
	}

	return marketspaces, nil
}

// GetFilteredMarketData fetches and displays market data from the matchings table in a structured 15-column format
// organized into bid and offer sections with OHLC data, volume totals, and price changes between periods.
//
// Bid Section (Buy Orders - order_type_id = 1): Contract Period, Bid Open, Bid High, Bid Low, Bid Vol., Bid, Bid Chg., Bid Spread
// Offer Section (Sell Orders - order_type_id = 2): Offer Open, Offer High, Offer Low, Offer Vol., Offer, Offer Chg., Offer Spread
//
// The function supports time-based filtering (1D, 1W, 1M, 1Y) and aggregates data by contract period (month/year).
// Results are sorted chronologically by contract period and include percentage changes from previous periods.
func (r *repository) GetFilteredMarketData(ctx context.Context, filter MarketDataFilter) ([]MarketData, error) {
	log.Printf("Getting market data with filter: %+v", filter)

	// First, check if the parameters are valid
	log.Printf("Checking if delivery_term_id %d exists", filter.DeliveryTermID)
	var deliveryTermExists bool
	deliveryTermQuery := `SELECT EXISTS(SELECT 1 FROM delivery_terms WHERE id = $1)`
	err := r.db.QueryRowContext(ctx, deliveryTermQuery, filter.DeliveryTermID).Scan(&deliveryTermExists)
	if err != nil {
		log.Printf("Error checking delivery term existence: %v", err)
	} else if !deliveryTermExists && filter.DeliveryTermID > 0 {
		log.Printf("Warning: Delivery term ID %d does not exist", filter.DeliveryTermID)
	}

	// First, check if we have any matching data
	countQuery := `
	SELECT COUNT(*) FROM matchings m
	WHERE m.product_id = $1
	  AND ($2 = 0 OR m.created_at >= CURRENT_DATE - $2 * INTERVAL '1 day')
	  AND ($3 = 0 OR m.market_id = $3)
	  AND ($4 = 0 OR m.marketspace_id = $4)
	  AND ($5 = 0 OR m.quality_id = $5)
	  AND ($6 = 0 OR m.delivery_term_id = $6)
	  AND ($7 = 0 OR m.payment_term_id = $7)
	  AND ($8 = 0 OR m.contract_type_id = $8)`

	var count int
	err = r.db.QueryRowContext(ctx, countQuery,
		filter.ProductID,
		filter.Days,
		filter.MarketID,
		filter.MarketspaceID,
		filter.QualityID,
		filter.DeliveryTermID,
		filter.PaymentTermID,
		filter.ContractTypeID,
	).Scan(&count)

	if err != nil {
		return nil, fmt.Errorf("error checking for matching data: %v", err)
	}

	log.Printf("Found %d matching records for the given filters", count)

	if count == 0 {
		// No matching data, return empty result
		return []MarketData{}, nil
	}

	// Updated query with improved market data analysis
	query := `
	WITH monthly_trades AS (
		SELECT
			DATE_TRUNC('month', m.first_delivery_date) as contract_month,
			m.order_type_id,
			m.price,
			m.quantity,
			m.created_at,
			COALESCE(m.updated_at, m.created_at) as sort_timestamp,
			m.updated_at
		FROM matchings m
		WHERE m.product_id = $1
		AND ($2 = 0 OR m.created_at >= CURRENT_DATE - $2 * INTERVAL '1 day')
		AND ($3 = 0 OR m.market_id = $3)
		AND ($4 = 0 OR m.marketspace_id = $4)
		AND ($5 = 0 OR m.quality_id = $5)
		AND ($6 = 0 OR m.delivery_term_id = $6)
		AND ($7 = 0 OR m.payment_term_id = $7)
		AND ($8 = 0 OR m.contract_type_id = $8)
	),
	monthly_open_prices AS (
		SELECT DISTINCT
			contract_month,
			order_type_id,
			FIRST_VALUE(price) OVER (
				PARTITION BY contract_month, order_type_id
				ORDER BY sort_timestamp ASC
			) AS open_price
		FROM monthly_trades
	),
	monthly_summary AS (
		SELECT
			mt.contract_month,
			TO_CHAR(mt.contract_month, 'Mon YYYY') as contract_period,
			MAX(mt.sort_timestamp) as latest_activity,
			MAX(CASE WHEN mop.order_type_id = 1 THEN mop.open_price END) as bid_open,
			MAX(CASE WHEN mt.order_type_id = 1 THEN mt.price END) as bid_high,
			MIN(CASE WHEN mt.order_type_id = 1 THEN mt.price END) as bid_low,
			SUM(CASE WHEN mt.order_type_id = 1 THEN mt.quantity ELSE 0 END) as bid_volume,
			AVG(CASE WHEN mt.order_type_id = 1 THEN mt.price END) as bid_price,
			COUNT(CASE WHEN mt.order_type_id = 1 THEN 1 END) as bid_count,
			MAX(CASE WHEN mop.order_type_id = 2 THEN mop.open_price END) as offer_open,
			MAX(CASE WHEN mt.order_type_id = 2 THEN mt.price END) as offer_high,
			MIN(CASE WHEN mt.order_type_id = 2 THEN mt.price END) as offer_low,
			SUM(CASE WHEN mt.order_type_id = 2 THEN mt.quantity ELSE 0 END) as offer_volume,
			AVG(CASE WHEN mt.order_type_id = 2 THEN mt.price END) as offer_price,
			COUNT(CASE WHEN mt.order_type_id = 2 THEN 1 END) as offer_count,
			COUNT(*) as total_trades,
			SUM(mt.quantity) as total_volume
		FROM monthly_trades mt
		LEFT JOIN monthly_open_prices mop 
			ON mt.contract_month = mop.contract_month AND mt.order_type_id = mop.order_type_id
		GROUP BY mt.contract_month
	),
	final_summary AS (
		SELECT
			contract_period,
			latest_activity,
			contract_month,
			bid_open,
			bid_high,
			bid_low,
			bid_volume,
			bid_price,
			bid_count,
			-- Calculate percentage change from previous period for bid
			CASE
				WHEN LAG(bid_price) OVER (ORDER BY contract_month) > 0
				THEN ((bid_price - LAG(bid_price) OVER (ORDER BY contract_month)) / LAG(bid_price) OVER (ORDER BY contract_month)) * 100
				ELSE 0.0
			END AS bid_change,
			offer_open,
			offer_high,
			offer_low,
			offer_volume,
			offer_price,
			offer_count,
			-- Calculate percentage change from previous period for offer
			CASE
				WHEN LAG(offer_price) OVER (ORDER BY contract_month) > 0
				THEN ((offer_price - LAG(offer_price) OVER (ORDER BY contract_month)) / LAG(offer_price) OVER (ORDER BY contract_month)) * 100
				ELSE 0.0
			END AS offer_change,
			total_trades,
			total_volume,
			CASE
				WHEN latest_activity >= CURRENT_DATE - INTERVAL '1 day' THEN 'Active Today'
				WHEN latest_activity >= CURRENT_DATE - INTERVAL '7 days' THEN 'Active This Week'
				WHEN latest_activity >= CURRENT_DATE - INTERVAL '30 days' THEN 'Active This Month'
				ELSE 'Inactive'
			END as activity_status
		FROM monthly_summary
	)
	SELECT
		contract_period,
		CAST(COALESCE(bid_open, 0.0) AS numeric(10,2)) as bid_open,
		CAST(COALESCE(bid_high, 0.0) AS numeric(10,2)) as bid_high,
		CAST(COALESCE(bid_low, 0.0) AS numeric(10,2)) as bid_low,
		CAST(COALESCE(bid_volume, 0.0) AS numeric(10,2)) as bid_volume,
		CAST(COALESCE(bid_price, 0.0) AS numeric(10,2)) as bid,
		CAST(COALESCE(bid_change, 0.0) AS numeric(10,2)) as bid_change,
		CAST(COALESCE(bid_high - bid_low, 0.0) AS numeric(10,2)) as bid_spread,
		CAST(COALESCE(offer_open, 0.0) AS numeric(10,2)) as offer_open,
		CAST(COALESCE(offer_high, 0.0) AS numeric(10,2)) as offer_high,
		CAST(COALESCE(offer_low, 0.0) AS numeric(10,2)) as offer_low,
		CAST(COALESCE(offer_volume, 0.0) AS numeric(10,2)) as offer_volume,
		CAST(COALESCE(offer_price, 0.0) AS numeric(10,2)) as offer,
		CAST(COALESCE(offer_change, 0.0) AS numeric(10,2)) as offer_change,
		CAST(COALESCE(offer_high - offer_low, 0.0) AS numeric(10,2)) as offer_spread
	FROM final_summary
	ORDER BY contract_month ASC;`

	rows, err := r.db.QueryContext(ctx, query,
		filter.ProductID,
		filter.Days,
		filter.MarketID,
		filter.MarketspaceID,
		filter.QualityID,
		filter.DeliveryTermID,
		filter.PaymentTermID,
		filter.ContractTypeID,
	)
	if err != nil {
		return nil, fmt.Errorf("error querying market data: %v", err)
	}
	defer rows.Close()

	var results []MarketData
	for rows.Next() {
		var md MarketData
		err := rows.Scan(
			&md.ContractPeriod,
			&md.BidOpen,
			&md.BidHigh,
			&md.BidLow,
			&md.BidVolume,
			&md.Bid,
			&md.BidChange,
			&md.BidSpread,
			&md.OfferOpen,
			&md.OfferHigh,
			&md.OfferLow,
			&md.OfferVolume,
			&md.Offer,
			&md.OfferChange,
			&md.OfferSpread,
		)

		if err != nil {
			log.Printf("Error scanning market data row: %v", err)
			return nil, fmt.Errorf("error scanning market data row: %v", err)
		}

		// Debug log to see what data we're getting
		log.Printf("Scanned market data row: %+v", md)
		results = append(results, md)
	}

	return results, nil
}

func (r *repository) GetPriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error) {
	log.Printf("Getting price history with filter: %+v", filter)

	// First, check if we have any matching data
	countQuery := `
	SELECT COUNT(*) FROM matchings m
	WHERE m.product_id = $1
	  AND m.created_at >= CURRENT_DATE - $2 * INTERVAL '1 day'
	  AND ($3 = 0 OR m.market_id = $3)
	  AND ($4 = 0 OR m.marketspace_id = $4)
	  AND ($5 = 0 OR m.quality_id = $5)
	  AND ($6 = 0 OR m.delivery_term_id = $6)
	  AND ($7 = 0 OR m.payment_term_id = $7)
	  AND ($8 = 0 OR m.contract_type_id = $8)`

	var count int
	err := r.db.QueryRowContext(ctx, countQuery,
		filter.ProductID,
		filter.Days,
		filter.MarketID,
		filter.MarketspaceID,
		filter.QualityID,
		filter.DeliveryTermID,
		filter.PaymentTermID,
		filter.ContractTypeID,
	).Scan(&count)

	if err != nil {
		return nil, fmt.Errorf("error checking for matching data: %v", err)
	}

	log.Printf("Found %d matching records for price history", count)

	if count == 0 {
		// No matching data, return empty result
		return []PriceHistoryPoint{}, nil
	}

	// Determine the appropriate time grouping based on days
	var timeFormat, timeGroup string
	if filter.Days <= 1 {
		// For 1 day, group by hour
		timeFormat = "HH24:MI"
		timeGroup = "hour"
	} else if filter.Days <= 7 {
		// For 1 week, group by day and hour
		timeFormat = "YYYY-MM-DD HH24:MI"
		timeGroup = "hour"
	} else if filter.Days <= 30 {
		// For 1 month, group by day
		timeFormat = "YYYY-MM-DD"
		timeGroup = "day"
	} else {
		// For 1 year, group by week
		timeFormat = "YYYY-MM-DD"
		timeGroup = "week"
	}

	// Query for price history data
	query := fmt.Sprintf(`
	WITH time_series AS (
	  SELECT
		DATE_TRUNC('%s', m.created_at) as time_point,
		TO_CHAR(m.created_at, '%s') as formatted_date,
		m.order_type_id,
		m.price,
		m.quantity
	  FROM matchings m
	  WHERE m.product_id = $1
		AND m.created_at >= CURRENT_DATE - $2 * INTERVAL '1 day'
		AND ($3 = 0 OR m.market_id = $3)
		AND ($4 = 0 OR m.marketspace_id = $4)
		AND ($5 = 0 OR m.quality_id = $5)
		AND ($6 = 0 OR m.delivery_term_id = $6)
		AND ($7 = 0 OR m.payment_term_id = $7)
		AND ($8 = 0 OR m.contract_type_id = $8)
	)
	SELECT
	  formatted_date as date,
	  CAST(COALESCE(AVG(CASE WHEN order_type_id = 1 THEN price END), 0.0) AS numeric(10,2)) as bid_price,
	  CAST(COALESCE(AVG(CASE WHEN order_type_id = 2 THEN price END), 0.0) AS numeric(10,2)) as offer_price,
	  CAST(COALESCE(SUM(CASE WHEN order_type_id = 1 THEN quantity ELSE 0 END), 0.0) AS numeric(10,2)) as bid_volume,
	  CAST(COALESCE(SUM(CASE WHEN order_type_id = 2 THEN quantity ELSE 0 END), 0.0) AS numeric(10,2)) as offer_volume,
	  CAST(COALESCE(
		SUM(CASE WHEN order_type_id = 1 THEN price * quantity ELSE 0 END) +
		SUM(CASE WHEN order_type_id = 2 THEN price * quantity ELSE 0 END), 0.0
	  ) AS numeric(14,2)) as market_cap
	FROM time_series
	GROUP BY time_point, formatted_date
	ORDER BY time_point ASC`, timeGroup, timeFormat)

	rows, err := r.db.QueryContext(ctx, query,
		filter.ProductID,
		filter.Days,
		filter.MarketID,
		filter.MarketspaceID,
		filter.QualityID,
		filter.DeliveryTermID,
		filter.PaymentTermID,
		filter.ContractTypeID,
	)
	if err != nil {
		return nil, fmt.Errorf("error querying price history: %v", err)
	}
	defer rows.Close()

	var results []PriceHistoryPoint
	for rows.Next() {
		var point PriceHistoryPoint
		err := rows.Scan(
			&point.Date,
			&point.BidPrice,
			&point.OfferPrice,
			&point.BidVolume,
			&point.OfferVolume,
			&point.MarketCap,
		)

		if err != nil {
			log.Printf("Error scanning price history row: %v", err)
			return nil, fmt.Errorf("error scanning price history row: %v", err)
		}

		results = append(results, point)
	}

	return results, nil
}

func (r *repository) GetRealtimePriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error) {
	// Fix the query to use the correct column names
	// The error shows that m.bid_price and m.offer_price don't exist
	query := `
	WITH time_series AS (
	  SELECT
		DATE_TRUNC('hour', m.created_at) as time_point,
		TO_CHAR(m.created_at, 'YYYY-MM-DD HH24:MI') as formatted_date,
		m.order_type_id,
		m.price,
		m.quantity
	  FROM matchings m
	  WHERE m.product_id = $1
		AND m.created_at >= CURRENT_DATE - $2 * INTERVAL '1 day'
		AND ($3 = 0 OR m.market_id = $3)
		AND ($4 = 0 OR m.marketspace_id = $4)
		AND ($5 = 0 OR m.quality_id = $5)
		AND ($6 = 0 OR m.delivery_term_id = $6)
		AND ($7 = 0 OR m.payment_term_id = $7)
		AND ($8 = 0 OR m.contract_type_id = $8)
	)
	SELECT
	  formatted_date as date,
	  CAST(COALESCE(AVG(CASE WHEN order_type_id = 1 THEN price END), 0.0) AS numeric(10,2)) as bid_price,
	  CAST(COALESCE(AVG(CASE WHEN order_type_id = 2 THEN price END), 0.0) AS numeric(10,2)) as offer_price,
	  CAST(COALESCE(SUM(CASE WHEN order_type_id = 1 THEN quantity ELSE 0 END), 0.0) AS numeric(10,2)) as bid_volume,
	  CAST(COALESCE(SUM(CASE WHEN order_type_id = 2 THEN quantity ELSE 0 END), 0.0) AS numeric(10,2)) as offer_volume,
	  CAST(COALESCE(
		SUM(CASE WHEN order_type_id = 1 THEN price * quantity ELSE 0 END) +
		SUM(CASE WHEN order_type_id = 2 THEN price * quantity ELSE 0 END), 0.0
	  ) AS numeric(14,2)) as market_cap
	FROM time_series
	GROUP BY time_point, formatted_date
	ORDER BY time_point ASC`

	rows, err := r.db.QueryContext(ctx, query,
		filter.ProductID,
		filter.Days,
		filter.MarketID,
		filter.MarketspaceID,
		filter.QualityID,
		filter.DeliveryTermID,
		filter.PaymentTermID,
		filter.ContractTypeID)
	if err != nil {
		return nil, fmt.Errorf("error querying real-time price history: %w", err)
	}
	defer rows.Close()

	var priceHistory []PriceHistoryPoint
	for rows.Next() {
		var point PriceHistoryPoint
		err := rows.Scan(
			&point.Date,
			&point.BidPrice,
			&point.OfferPrice,
			&point.BidVolume,
			&point.OfferVolume,
			&point.MarketCap,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning real-time price history row: %w", err)
		}
		priceHistory = append(priceHistory, point)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating real-time price history rows: %w", err)
	}

	return priceHistory, nil
}

// GetProductDetails retrieves detailed information about a product including market and submarket data
func (r *repository) GetProductDetails(ctx context.Context, productID int, lang string) (*ProductDetails, error) {
	query := `
		SELECT
			p.id,
			COALESCE(p.en_name, '') as en_name,
			COALESCE(p.th_name, '') as th_name,
			COALESCE(p.en_description, '') as en_description,
			COALESCE(p.th_description, '') as th_description,
			COALESCE(p.market_id, 0) as market_id,
			COALESCE(m.en_name, '') as market_en_name,
			COALESCE(m.th_name, '') as market_th_name,
			p.submarket_id,
			COALESCE(sm.en_name, '') as submarket_en_name,
			COALESCE(sm.th_name, '') as submarket_th_name
		FROM products p
		LEFT JOIN markets m ON p.market_id = m.id
		LEFT JOIN submarkets sm ON p.submarket_id = sm.id
		WHERE p.id = $1
	`

	var details ProductDetails
	var submarketID sql.NullInt64

	err := r.db.QueryRowContext(ctx, query, productID).Scan(
		&details.ID,
		&details.EnName,
		&details.ThName,
		&details.EnDescription,
		&details.ThDescription,
		&details.MarketID,
		&details.MarketEnName,
		&details.MarketThName,
		&submarketID,
		&details.SubmarketEnName,
		&details.SubmarketThName,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product with ID %d not found", productID)
		}
		return nil, fmt.Errorf("error querying product details: %v", err)
	}

	// Handle nullable submarket ID
	if submarketID.Valid {
		submarketIDInt := int(submarketID.Int64)
		details.SubmarketID = &submarketIDInt
	}

	// Fetch product qualities
	qualities, err := r.GetProductQualities(ctx, productID, lang)
	if err != nil {
		// Log the error but don't fail the entire request
		fmt.Printf("Warning: Failed to fetch qualities for product %d: %v\n", productID, err)
		details.Qualities = []QualityOption{} // Empty array instead of nil
	} else {
		details.Qualities = qualities
	}

	// Set clickable product name functionality
	details.IsClickable = true
	details.ProductPageURL = fmt.Sprintf("/productspage?lang=%s", lang)

	return &details, nil
}
