package markets

import (
	"context"
	"fmt"
)

type Service interface {
	SearchProducts(ctx context.Context, params ProductSearchParams) ([]ProductSearchResult, error)
	GetProductQualities(ctx context.Context, productID int, lang string) ([]QualityOption, error)
	GetDeliveryTerms(ctx context.Context, isLocal bool) ([]DeliveryTermOption, error)
	GetMarkets(ctx context.Context) ([]MarketOption, error)
	GetContractTypes(ctx context.Context) ([]ContractTypeOption, error)
	GetPaymentTerms(ctx context.Context, isLocal bool) ([]PaymentTermOption, error)
	GetMarketspaces(ctx context.Context) ([]MarketspaceOption, error)
	GetFilteredMarketData(ctx context.Context, filter MarketDataFilter) ([]MarketData, error)
	GetPriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error)
	GetRealtimePriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error)
	GetProductDetails(ctx context.Context, productID int, lang string) (*ProductDetails, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) SearchProducts(ctx context.Context, params ProductSearchParams) ([]ProductSearchResult, error) {
	// Validate and set defaults for search parameters
	if params.Limit <= 0 {
		params.Limit = 10 // Default limit
	}

	if params.Language == "" {
		params.Language = "en" // Default to English
	}

	// Call repository
	return s.repo.SearchProducts(ctx, params)
}

func (s *service) GetProductQualities(ctx context.Context, productID int, lang string) ([]QualityOption, error) {
	// Validate parameters
	if productID <= 0 {
		return nil, fmt.Errorf("invalid product ID: must be greater than 0")
	}

	// Set default language if empty
	if lang == "" {
		lang = "en"
	}

	// Validate language value
	if lang != "en" && lang != "th" {
		return nil, fmt.Errorf("unsupported language: must be 'en' or 'th'")
	}

	qualities, err := s.repo.GetProductQualities(ctx, productID, lang)
	if err != nil {
		return nil, fmt.Errorf("failed to get product qualities: %v", err)
	}

	// Add debug logging
	for _, q := range qualities {
		fmt.Printf("Quality: ID=%d, Standard=%s, Grade=%s\n",
			q.ID, q.StandardEnName, q.GradeEnName)
	}

	return qualities, nil
}

func (s *service) GetDeliveryTerms(ctx context.Context, isLocal bool) ([]DeliveryTermOption, error) {
	return s.repo.GetDeliveryTerms(ctx, isLocal)
}

func (s *service) GetMarkets(ctx context.Context) ([]MarketOption, error) {
	return s.repo.GetMarkets(ctx)
}

func (s *service) GetContractTypes(ctx context.Context) ([]ContractTypeOption, error) {
	return s.repo.GetContractTypes(ctx)
}

func (s *service) GetPaymentTerms(ctx context.Context, isLocal bool) ([]PaymentTermOption, error) {
	// Call repository method
	return s.repo.GetPaymentTerms(ctx, isLocal)
}

func (s *service) GetMarketspaces(ctx context.Context) ([]MarketspaceOption, error) {
	return s.repo.GetMarketspaces(ctx)
}

func (s *service) GetFilteredMarketData(ctx context.Context, filter MarketDataFilter) ([]MarketData, error) {
	return s.repo.GetFilteredMarketData(ctx, filter)
}

func (s *service) GetPriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error) {
	return s.repo.GetPriceHistory(ctx, filter)
}

// GetRealtimePriceHistory gets the most up-to-date price history data
func (s *service) GetRealtimePriceHistory(ctx context.Context, filter PriceHistoryFilter) ([]PriceHistoryPoint, error) {
	// This method should query the database for the most recent data
	// It should be optimized for speed since it will be called frequently
	return s.repo.GetRealtimePriceHistory(ctx, filter)
}

// GetProductDetails retrieves detailed information about a product
func (s *service) GetProductDetails(ctx context.Context, productID int, lang string) (*ProductDetails, error) {
	// Validate productID
	if productID <= 0 {
		return nil, fmt.Errorf("invalid product ID: must be greater than 0")
	}

	// Set default language if empty
	if lang == "" {
		lang = "en"
	}

	// Validate language value
	if lang != "en" && lang != "th" {
		return nil, fmt.Errorf("unsupported language: must be 'en' or 'th'")
	}

	// Call repository method
	details, err := s.repo.GetProductDetails(ctx, productID, lang)
	if err != nil {
		return nil, fmt.Errorf("failed to get product details: %v", err)
	}

	return details, nil
}
