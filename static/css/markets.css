
    .product-overview-container {
      margin: 20px 0;
      width: 100%;
    }

    .product-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 15px;
    }

    .product-market-tag {
      background-color: #96FE00;
      color: rgb(0, 0, 0);
      padding: 4px 10px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .product-info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-bottom: 15px;
    }

    .product-info-item {
      padding: 10px;
      background-color: rgba(52, 152, 219, 0.05);
      border-radius: 4px;
    }

    .info-label {
      font-size: 13px;
      color: #7f8c8d;
      display: block;
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 15px;
      color: #2c3e50;
      font-weight: 500;
    }

    .product-description {
      margin-top: 15px;
      padding: 15px;
      border-radius: 4px;
      background-color: rgba(52, 152, 219, 0.05);
      color: #34495e;
      font-size: 14px;
      line-height: 1.5;
    }

    /* Loading and error states for product overview */
    .loading-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;
      color: #7f8c8d;
    }

    .loading-container .spinner {
      margin-bottom: 15px;
    }

    .error-container {
      color: #e74c3c;
    }

    .product-overview-content {
      display: block;
    }

    /* Product description styling */
    .product-description {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
    }

    .product-description h4 {
      margin: 0 0 10px 0;
      color: #2c3e50;
      font-size: 16px;
      font-weight: 600;
    }

    .product-description p {
      margin: 0;
      color: #555;
      line-height: 1.6;
      text-align: justify;
    }

    /* Responsive styles for smaller screens */
    @media screen and (max-width: 768px) {
      .product-info-grid {
        grid-template-columns: 1fr;
      }

      .product-description p {
        text-align: left;
      }
    }

    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 15px;
      background-color: white;
      border: 1px solid #f0f9e0;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      padding: 15px;
      transition: height 0.3s ease;
    }

    .chart-loading,
    .chart-no-data {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.9);
      z-index: 10;
      border-radius: 8px;
      font-size: 14px;
      text-align: center;
      padding: 20px;
    }

    .chart-loading .spinner {
      margin-bottom: 10px;
    }

    /* Responsive chart adjustments */
    @media screen and (max-width: 992px) {
      .chart-container {
        height: 280px;
        padding: 12px;
      }
    }

    @media screen and (max-width: 768px) {
      .chart-container {
        height: 250px;
        padding: 10px;
      }

      .chart-loading,
      .chart-no-data {
        font-size: 13px;
        padding: 15px;
      }
    }

    @media screen and (max-width: 480px) {
      .chart-container {
        height: 220px;
        padding: 8px;
        margin-bottom: 10px;
      }

      .chart-loading,
      .chart-no-data {
        font-size: 12px;
        padding: 10px;
      }

      .chart-loading .spinner {
        width: 24px;
        height: 24px;
      }
    }

    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .time-filter {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-top: 10px;
    }

    .time-btn {
      padding: 5px 15px;
      border: 1px solid #ddd;
      background-color: #f8f8f8;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .time-btn.active {
      background-color: #3498db;
      color: white;
      border-color: #3498db;
    }


  /* Main dashboard styling */
  .main-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .main-dashboard h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    position: relative;
  }

  .main-dashboard h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background: #96FE00;
    margin: 10px auto;
  }

  /* Search container base styles */
  .search-container {
    position: relative;
    margin: 20px auto 30px;
    width: 100%;
    max-width: 1200px;
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  /* Search header styles */
  .search-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
  }

  .search-header h3 {
    margin: 0 0 8px;
    color: #2c3e50;
    font-size: 20px;
  }

  .search-header p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
  }

  /* Search main content */
  .search-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* Input group for search and indicator */
  .input-group {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .input-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
  }

  .search-input-wrapper {
    position: relative;
    width: 100%;
  }

  /* Product search input */
  #productSearch {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  #productSearch:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    outline: none;
  }

  /* Style for when a product is selected */
  #productSearch.product-selected {
    border-color: #28a745;
    background-color: #f8fff8;
  }

  /* Selected product indicator */
  .selected-product-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #28a745;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }

  /* Filter section */
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  /* Filter rows */
  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  /* Filter group */
  .filter-group {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
  }

  /* Filter select boxes */
  .filter-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
  }

  /* Search action section */
  .search-action {
    display: flex;
    justify-content: flex-end;
  }

  /* Search button */
  .search-btn {
    padding: 14px 28px;
    background: #96FE00; /* Your specified green color */
    color: #333; /* Dark text for better contrast on light green */
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3);
    position: relative;
    overflow: hidden;
  }

  .search-btn:hover {
    background: #86E400; /* Slightly darker on hover */
    box-shadow: 0 4px 8px rgba(150, 254, 0, 0.4);
    transform: translateY(-1px);
  }

  /* Update time filter buttons */
  .time-filter-btn.active,
  .time-btn.active {
    background: #96FE00;
    color: #333;
    border-color: #96FE00;
    font-weight: bold;
  }

  .time-filter-btn:hover,
  .time-btn:hover {
    background: #f0f9e0;
    border-color: #96FE00;
  }

  /* Update LIVE indicator to match the theme */
  #realtime-indicator {
    background-color: #96FE00 !important;
    color: #333 !important;
    border: 1px solid rgba(150, 254, 0, 0.5) !important;
    font-weight: bold;
    padding: 4px 8px !important;
    border-radius: 4px !important;
  }

  /* Style the chart container with subtle green accents */
  .chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 10px;
    background-color: white;
    border: 1px solid #f0f9e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 15px;
  }

  /* Style the analytics cards with green accents */
  .analytics-card {
    border-left: 4px solid #96FE00;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .analytics-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .analytics-card h3 {
    color: #333;
    border-bottom: 1px solid #f0f9e0;
    padding-bottom: 10px;
    margin-top: 0;
  }

  /* Style the time filter buttons */
  .time-filter {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  .time-btn {
    padding: 6px 16px;
    border: 1px solid #e0e0e0;
    background-color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }

  .time-btn.active {
    background-color: #96FE00;
    color: #333;
    border-color: #96FE00;
    font-weight: bold;
  }

  /* Style the product search input */
  #productSearch {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  #productSearch:focus {
    border-color: #96FE00;
    box-shadow: 0 0 0 3px rgba(150, 254, 0, 0.25);
    outline: none;
  }

  /* Style for when a product is selected */
  #productSearch.product-selected {
    border-color: #96FE00;
    background-color: #f9fff0;
  }

  /* Style the filter selects */
  .filter-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
  }

  .filter-select:focus {
    border-color: #96FE00;
    box-shadow: 0 0 0 2px rgba(150, 254, 0, 0.25);
    outline: none;
  }

  /* Add a subtle green background to the search container */
  .search-container {
    background: linear-gradient(to bottom, white, #f9fff0);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }

  /* Style the search header */
  .search-header {
    border-bottom: 2px solid #f0f9e0;
  }

  .search-header h3 {
    color: #333;
    font-weight: bold;
  }

  /* Add a subtle animation to the search button */
  @keyframes gentle-pulse {
    0% { box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3); }
    50% { box-shadow: 0 4px 8px rgba(150, 254, 0, 0.5); }
    100% { box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3); }
  }

  .search-btn.pulse-animation {
    animation: gentle-pulse 2s infinite;
  }

  /* Time filter container */
  .time-filter-container {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .time-filter-btn {
    padding: 8px 16px;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .time-filter-btn:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
  }

  .time-filter-btn.active {
    background: #96FE00;
    color: rgb(0, 0, 0);
    border-color: #96FE00;
  }

  /* Futures table wrapper */
  .futures-table-wrapper {
    position: relative;
  }

  .futures-table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }

  /* Loading and error states */
  .futures-table tr.loading td {
    text-align: center;
    padding: 2em;
    background: rgba(255, 255, 255, 0.8);
  }

  .futures-table tr.error td {
    text-align: center;
    padding: 2em;
    color: #dc3545;
    background: #fff;
  }

  /* Price changes */
  .price-up {
    color: #28a745;
  }

  .price-down {
    color: #dc3545;
  }

  /* Loading spinner */
  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* Add these to your existing <style> section */
  .futures-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
  }

  .futures-table th,
  .futures-table td {
    padding: 10px;
    text-align: right;
    border: 1px solid #ddd;
    font-size: 14px;
  }

  .futures-table th {
    background-color: #f8f9fa;
    font-weight: bold;
  }

  .price-up {
    color: #28a745;
  }

  .price-down {
    color: #dc3545;
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* Responsive Design */
  @media screen and (max-width: 1200px) {
    .search-container {
      max-width: 95%;
      padding: 20px;
    }

    .filter-select {
      min-width: 180px;
    }
  }

  @media screen and (max-width: 992px) {
    .filter-row {
      gap: 12px;
    }

    .filter-select {
      min-width: calc(33.333% - 12px);
      flex: 0 0 calc(33.333% - 12px);
    }

    #productSearch {
      padding: 12px 16px;
      font-size: 15px;
    }
  }

  @media screen and (max-width: 768px) {
    .search-container {
      padding: 15px;
      margin: 15px auto;
    }

    .filter-select {
      min-width: calc(50% - 8px);
      flex: 0 0 calc(50% - 8px);
      padding: 10px;
      font-size: 13px;
    }

    .advanced-search {
      padding: 15px;
      margin-top: 12px;
    }

    .search-btn {
      padding: 12px;
      font-size: 14px;
    }
  }

  @media screen and (max-width: 480px) {
    .search-container {
      padding: 12px;
      margin: 10px auto;
    }

    .filter-select {
      min-width: 100%;
      flex: 0 0 100%;
      margin-bottom: 8px;
    }

    #productSearch {
      padding: 10px;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .advanced-search {
      padding: 12px;
      margin-top: 10px;
    }

    .filter-row {
      gap: 8px;
      margin-bottom: 8px;
    }

    .search-btn {
      padding: 10px;
      font-size: 13px;
    }
  }

  /* Hover and focus states */
  .filter-select:hover:not(:disabled) {
    border-color: #6ee663;
  }

  .filter-select:focus:not(:disabled) {
    outline: none;
    border-color: #42b635;
    box-shadow: 0 0 0 2px rgba(111, 235, 130, 0.25);
  }

  .search-btn:hover {
    background: #42b635;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .search-btn:active {
    background: #59c464;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transform: translateY(1px);
  }

  /* Disabled state */
  .filter-select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.8;
  }

  /* Search results dropdown */
  .search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    display: none;
  }

  .search-results ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .search-results li {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .search-results li:last-child {
    border-bottom: none;
  }

  .search-results li:hover {
    background-color: #f8f9fa;
  }

  .search-results .product-info {
    display: flex;
    flex-direction: column;
  }

  .search-results .product-info strong {
    color: #2c3e50;
    margin-bottom: 2px;
  }

  .search-results .product-info .th-name {
    color: #7f8c8d;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .search-results .product-info .market-name {
    color: #3498db;
    font-size: 12px;
  }

  .search-results .no-results,
  .search-results .error {
    padding: 15px;
    text-align: center;
    color: #7f8c8d;
  }

  .search-results .error {
    color: #e74c3c;
  }

  /* Form control styling for consistency */
  .form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
  }

  .form-control:focus {
    border-color: #96FE00;
    box-shadow: 0 0 0 3px rgba(150, 254, 0, 0.25);
    outline: none;
  }

  /* Futures table wrapper for better scrolling */
  .futures-table-wrapper {
    overflow-x: auto;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* Trading Actions Styles - Minimalistic Design */
  .trading-actions-container {
    margin-top: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 12px;
    border: 1px solid #e8e8e8;
  }

  .trading-actions-header {
    text-align: center;
    margin-bottom: 16px;
  }

  .trading-actions-header h4 {
    color: #2c3e50;
    margin: 0 0 4px 0;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .trading-actions-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.85rem;
  }

  .trading-actions-buttons {
    display: flex;
    gap: 0;
    justify-content: center;
    align-items: center;
    background: white;
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .trading-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    background: transparent;
    color: #6c757d;
    position: relative;
  }

  .trading-action-btn:hover {
    background: #f8f9fa;
  }

  .trading-action-btn:active {
    transform: scale(0.98);
  }

  .buy-order-btn {
    color: #28a745;
  }

  .buy-order-btn:hover {
    background: #e8f5e8;
    color: #1e7e34;
  }

  .sell-order-btn {
    color: #dc3545;
  }

  .sell-order-btn:hover {
    background: #fdeaea;
    color: #bd2130;
  }

  .trading-action-btn .btn-icon {
    font-size: 1.1rem;
    margin-right: 6px;
  }

  .trading-action-btn .btn-text {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.3px;
  }

  .trading-action-btn .btn-subtext {
    display: none; /* Hide subtext for minimalistic design */
  }

  /* Clickable Product Name Styles */
  .clickable-product-name {
    color: #007bff;
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
  }

  .clickable-product-name:hover {
    color: #0056b3;
    text-decoration: none;
    border-bottom: 1px solid #0056b3;
    cursor: pointer;
  }

  .clickable-product-name:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    border-radius: 2px;
  }

  /* Ensure clickable product name maintains styling in different contexts */
  h3 .clickable-product-name {
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
  }

  h3 .clickable-product-name:hover {
    color: #007bff;
  }

  .info-value .clickable-product-name {
    color: #007bff;
    font-weight: 500;
  }

  .info-value .clickable-product-name:hover {
    color: #0056b3;
  }

  /* Enhanced responsive styles for markets pages */

  /* Tablet styles (768px to 992px) */
  @media screen and (min-width: 768px) and (max-width: 992px) {
    .search-container {
      padding: 20px;
    }

    .filter-row {
      gap: 12px;
    }

    .filter-group {
      min-width: 180px;
    }

    .product-info-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .analytics-card {
      padding: 15px;
    }

    .futures-table-wrapper {
      overflow-x: auto;
    }

    .futures-table th,
    .futures-table td {
      padding: 8px 6px;
      font-size: 13px;
    }

    /* Trading actions tablet styles */
    .trading-actions-container {
      margin: 12px;
      padding: 12px;
    }

    .trading-actions-header h4 {
      font-size: 1rem;
    }

    .trading-actions-header p {
      font-size: 0.8rem;
    }

    .trading-action-btn {
      padding: 10px 14px;
      font-size: 0.85rem;
    }
  }

  /* Mobile styles (up to 767px) */
  @media screen and (max-width: 767px) {
    .search-container {
      padding: 15px;
      margin: 15px auto;
    }

    .search-header h3 {
      font-size: 18px;
    }

    .search-header p {
      font-size: 13px;
    }

    .filter-section {
      padding: 15px;
    }

    .filter-row {
      flex-direction: column;
      gap: 10px;
    }

    .filter-group {
      width: 100%;
      min-width: 100%;
    }

    .search-action {
      margin-top: 15px;
    }

    .search-btn {
      width: 100%;
    }

    .product-info-grid {
      grid-template-columns: 1fr;
      gap: 10px;
    }

    .analytics-card h3 {
      font-size: 16px;
    }

    .time-filter-container {
      flex-wrap: wrap;
      justify-content: center;
    }

    .time-filter-btn {
      padding: 6px 12px;
      font-size: 13px;
      flex: 1 0 calc(50% - 10px);
      text-align: center;
      margin-bottom: 8px;
    }

    .time-filter {
      flex-wrap: wrap;
    }

    .time-btn {
      flex: 1 0 calc(50% - 10px);
      margin-bottom: 8px;
      text-align: center;
    }

    .chart-container {
      height: 250px;
    }

    /* Make futures table scrollable horizontally */
    .futures-table-wrapper {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .futures-table {
      min-width: 800px; /* Ensure table is wide enough to scroll */
    }

    .futures-table th,
    .futures-table td {
      padding: 8px 4px;
      font-size: 12px;
    }

    /* Trading actions mobile styles - Minimalistic */
    .trading-actions-container {
      margin: 8px;
      padding: 12px;
    }

    .trading-actions-header {
      margin-bottom: 12px;
    }

    .trading-actions-header h4 {
      font-size: 0.95rem;
    }

    .trading-actions-header p {
      font-size: 0.75rem;
    }

    .trading-actions-buttons {
      padding: 3px;
      flex-direction: row; /* Keep horizontal on mobile for minimalistic design */
    }

    .trading-action-btn {
      padding: 10px 12px;
      font-size: 0.8rem;
    }

    .trading-action-btn .btn-icon {
      font-size: 1rem;
      margin-right: 4px;
    }

    .trading-action-btn .btn-text {
      font-size: 0.8rem;
    }

    /* Clickable product name mobile styles */
    .clickable-product-name {
      font-size: 0.9rem;
    }

    h3 .clickable-product-name {
      font-size: inherit;
    }
  }

  /* Small mobile devices (up to 480px) */
  @media screen and (max-width: 480px) {
    .search-container {
      padding: 12px;
      margin: 10px auto;
    }

    .search-header h3 {
      font-size: 16px;
    }

    .search-header p {
      font-size: 12px;
    }

    .filter-section {
      padding: 12px;
    }

    .filter-group label {
      font-size: 13px;
    }

    .filter-select {
      padding: 10px;
      font-size: 13px;
    }

    .product-description {
      font-size: 13px;
      padding: 12px;
    }

    .info-label {
      font-size: 12px;
    }

    .info-value {
      font-size: 13px;
    }

    .chart-container {
      height: 200px;
      padding: 10px;
    }

    .time-filter-btn,
    .time-btn {
      padding: 5px 10px;
      font-size: 12px;
    }
  }

  /* Add CSS for new features */
  .search-clear-btn {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    z-index: 5;
  }

  .search-clear-btn:hover {
    color: #333;
  }

  .searching {
    background-image: url('/static/images/spinner.gif');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 20px;
  }

  .reset-filters-btn {
    margin-top: 10px;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
  }

  .reset-filters-btn:hover {
    background-color: #e9ecef;
  }

  .error-highlight {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25) !important;
  }

  .search-error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
    position: absolute;
    top: 100%;
    left: 0;
  }

  .highlight-filter, .highlight-required {
    animation: pulse-border 2s ease-in-out;
  }

  @keyframes pulse-border {
    0% { border-color: #96FE00; box-shadow: 0 0 0 0 rgba(150, 254, 0, 0.4); }
    70% { border-color: #96FE00; box-shadow: 0 0 0 10px rgba(150, 254, 0, 0); }
    100% { border-color: #ddd; box-shadow: 0 0 0 0 rgba(150, 254, 0, 0); }
  }

  .filter-tooltip {
    position: absolute;
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
  }

  .filter-tooltip:after {
    content: '';
    position: absolute;
    top: -5px;
    left: 10px;
    border-width: 0 5px 5px;
    border-style: solid;
    border-color: transparent transparent #333;
  }

  /* Simple Real-time Chart Styles */
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .chart-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
    font-weight: bold;
  }

  /* Simple Live Indicator */
  .live-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #96FE00;
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
  }

  .live-dot {
    width: 8px;
    height: 8px;
    background: #333;
    border-radius: 50%;
    animation: pulse-live 2s infinite;
  }

  @keyframes pulse-live {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .live-text {
    font-size: 11px;
  }

  /* Simple Update Indicator */
  .update-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(150, 254, 0, 0.9);
    color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    z-index: 10;
    animation: fadeInOut 2s ease-in-out;
  }

  @keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(20px); }
    20% { opacity: 1; transform: translateX(0); }
    80% { opacity: 1; transform: translateX(0); }
    100% { opacity: 0; transform: translateX(20px); }
  }

  /* Mobile responsive for real-time elements */
  @media screen and (max-width: 768px) {
    .chart-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .live-indicator {
      align-self: flex-end;
    }

    .update-indicator {
      top: 5px;
      right: 5px;
      padding: 3px 6px;
      font-size: 10px;
    }
  }
