{{ define "content" }}
<nav class="navigator">
  <a href="/?lang={{ .lang }}" class="nav-brand">BiomassX</a>
  <a href="/order?lang={{ .lang }}">Trade</a>
  <a href="/markets?lang={{ .lang }}">Markets</a>
  <a href="/productspage?lang={{ .lang }}">Products</a>
  <a href="/services?lang={{ .lang }}">Services</a>
  <a href="/login?lang={{ .lang }}">Login</a>
</nav>

<main class="main-dashboard">
  <h2>Markets</h2>

  <!-- Improved search bar with essential filters -->
  <div class="search-container">
    <div class="search-header">
      <h3>Search Market</h3>
      <p>Select a product and apply filters to view market data</p>
    </div>

    <div class="search-main">
      <!-- Product search with improved styling -->
      <div class="input-group">
        <label for="productSearch">Product</label>
        <div class="search-input-wrapper">
          <input type="text" id="productSearch" class="form-control" placeholder="Type to search products...">
          <div id="selectedProductIndicator" class="selected-product-indicator" style="display: none;">✓</div>
        </div>
        <div id="searchResults" class="search-results"></div>
      </div>

      <!-- Improved filter layout -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-group">
            <label for="marketFilter">Market</label>
            <select class="filter-select" id="marketFilter">
              <option value="">All Markets</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="localFilter">Marketspace</label>
            <select class="filter-select" id="localFilter">
              <option value="">All Marketspaces</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="qualityFilter">Quality</label>
            <select class="filter-select" id="qualityFilter" disabled>
              <option value="">All Qualities</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-group">
            <label for="deliveryTermFilter">Delivery Term</label>
            <select class="filter-select" id="deliveryTermFilter" disabled>
              <option value="">All Delivery Terms</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="paymentTermFilter">Payment Term</label>
            <select class="filter-select" id="paymentTermFilter" disabled>
              <option value="">All Payment Terms</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="contractFilter">Contract Type</label>
            <select class="filter-select" id="contractFilter">
              <option value="">All Contract Types</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Search button with improved styling -->
      <div class="search-action">
        <button id="searchSubmitBtn" class="search-btn">Search</button>
      </div>
    </div>
  </div>

  <!-- Product Overview Container - Initially Hidden -->
  <div id="productOverviewContainer" class="analytics-card product-overview-container" style="display: none;">
    <div id="productOverviewLoading" class="loading-container" style="display: none;">
      <div class="spinner"></div>
      <p>Loading product details...</p>
    </div>
    <div id="productOverviewContent" class="product-overview-content">
      <!-- Content will be populated dynamically by JavaScript -->
    </div>
    <div id="productOverviewError" class="error-container" style="display: none;">
      <p>Error loading product details. Please try again.</p>
    </div>



  <div class="analytics-card">
    <h3>End-of-day Quote</h3>
    <h5>Loading date...</h5>

    <div class="time-filter-container">
      <button class="time-filter-btn active" data-days="1">1 Day</button>
      <button class="time-filter-btn" data-days="7">1 Week</button>
      <button class="time-filter-btn" data-days="30">1 Month</button>
      <button class="time-filter-btn" data-days="365">1 Year</button>
    </div>

    <div class="futures-table-wrapper">
      <table class="futures-table">
        <thead>
          <tr>
            <th>Contract Period</th>
            <th>Open</th>
            <th>High</th>
            <th>Low</th>
            <th>Bid Vol.</th>
            <th>Bid</th>
            <th>Chg.</th>
            <th>Open</th>
            <th>High</th>
            <th>Low</th>
            <th>Offer Vol.</th>
            <th>Offer</th>
            <th>Chg.</th>
            <th>Spread</th>
          </tr>
        </thead>
        <tbody>
          <tr>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="analytics-card">
    <div class="chart-header">
      <h3>Price History Analysis</h3>
      <div class="live-indicator" id="liveIndicator" style="display: none;">
        <span class="live-dot"></span>
        <span class="live-text">Live Updates</span>
      </div>
    </div>
    <div class="chart-container">
      <canvas id="priceHistoryChart"></canvas>
      <div id="priceHistoryLoading" class="chart-loading" style="display: none;">
        <div class="spinner"></div>
        <p>Loading price data...</p>
      </div>
      <div id="priceHistoryNoData" class="chart-no-data" style="display: none;">
        <p>No price history data available for the selected criteria</p>
      </div>
      <div id="updateIndicator" class="update-indicator" style="display: none;">
        <span class="update-text">Updating...</span>
      </div>
    </div>
    <div class="time-filter">
      <button class="time-btn active" data-days="1">1D</button>
      <button class="time-btn" data-days="7">1W</button>
      <button class="time-btn" data-days="30">1M</button>
      <button class="time-btn" data-days="365">1Y</button>
    </div>
        <!-- Trading Actions Container - Minimalistic Design -->
    <div id="tradingActionsContainer" class="trading-actions-container" style="display: none;">
      <div class="trading-actions-header">
        <h4>Quick Trade</h4>
        <p>Choose your action</p>
      </div>
      <div class="trading-actions-buttons">
        <!-- Buy Order Form -->
        <form action="/markets/buy" method="POST" style="flex: 1;">
          <input type="hidden" name="lang" value="{{ .lang }}">
          <input type="hidden" name="product_id" id="buyProductId" value="">
          <button type="submit" class="trading-action-btn buy-order-btn">
            <span class="btn-icon">📈</span>
            <span class="btn-text">BUY</span>
          </button>
        </form>

        <!-- Sell Order Form -->
        <form action="/markets/sell" method="POST" style="flex: 1;">
          <input type="hidden" name="lang" value="{{ .lang }}">
          <input type="hidden" name="product_id" id="sellProductId" value="">
          <button type="submit" class="trading-action-btn sell-order-btn">
            <span class="btn-icon">📉</span>
            <span class="btn-text">SELL</span>
          </button>
        </form>
      </div>
    </div>
  </div>
  </div>

{{ end }}
