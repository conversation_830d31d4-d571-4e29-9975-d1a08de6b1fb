{{ define "content" }}
<nav class="navigator">
  <a href="/?lang={{ .lang }}" class="nav-brand">BiomassX</a>
  <a href="/order?lang={{ .lang }}">เสนอซื้อขาย</a>
  <a href="/markets?lang={{ .lang }}">ตลาด</a>
  <a href="/productspage?lang={{ .lang }}">สินค้า</a>
  <a href="/services?lang={{ .lang }}">บริการ</a>
  <a href="/login?lang={{ .lang }}">เข้าสู่ระบบ</a>
</nav>

<main class="main-dashboard">
  <h2>ตลาด</h2>

  <!-- Improved search bar with essential filters -->
  <div class="search-container">
    <div class="search-header">
      <h3>ค้นหาข้อมูลตลาด</h3>
      <p>เลือกสินค้าและใช้ตัวกรองเพื่อดูข้อมูลตลาด</p>
    </div>

    <div class="search-main">
      <!-- Product search with improved styling -->
      <div class="input-group">
        <label for="productSearch">สินค้า</label>
        <div class="search-input-wrapper">
          <input type="text" id="productSearch" class="form-control" placeholder="พิมพ์เพื่อค้นหาสินค้า...">
          <div id="selectedProductIndicator" class="selected-product-indicator" style="display: none;">✓</div>
        </div>
        <div id="searchResults" class="search-results"></div>
      </div>

      <!-- Improved filter layout -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-group">
            <label for="marketFilter">ตลาด</label>
            <select class="filter-select" id="marketFilter">
              <option value="">ตลาดทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="localFilter">พื้นที่ตลาด</label>
            <select class="filter-select" id="localFilter">
              <option value="">พื้นที่ตลาดทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="qualityFilter">คุณภาพ</label>
            <select class="filter-select" id="qualityFilter" disabled>
              <option value="">คุณภาพทั้งหมด</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-group">
            <label for="deliveryTermFilter">เงื่อนไขการส่งมอบ</label>
            <select class="filter-select" id="deliveryTermFilter" disabled>
              <option value="">เงื่อนไขการส่งมอบทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="paymentTermFilter">เงื่อนไขการชำระเงิน</label>
            <select class="filter-select" id="paymentTermFilter" disabled>
              <option value="">เงื่อนไขการชำระเงินทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="contractFilter">ประเภทสัญญา</label>
            <select class="filter-select" id="contractFilter">
              <option value="">ประเภทสัญญาทั้งหมด</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Search action button -->
      <div class="search-action">
        <button class="search-btn" id="searchSubmitBtn">ค้นหา</button>
      </div>
    </div>
  </div>

  <!-- Product Overview Container - Initially Hidden -->
  <div id="productOverviewContainer" class="analytics-card product-overview-container" style="display: none;">
    <div id="productOverviewLoading" class="loading-container" style="display: none;">
      <div class="spinner"></div>
      <p>กำลังโหลดรายละเอียดสินค้า...</p>
    </div>
    <div id="productOverviewContent" class="product-overview-content">
      <!-- Content will be populated dynamically by JavaScript -->
    </div>
    <div id="productOverviewError" class="error-container" style="display: none;">
      <p>เกิดข้อผิดพลาดในการโหลดรายละเอียดสินค้า กรุณาลองใหม่อีกครั้ง</p>
    </div>

    <!-- Trading Actions Container - Minimalistic Design -->
    <div id="tradingActionsContainer" class="trading-actions-container" style="display: none;">
      <div class="trading-actions-header">
        <h4>เทรดด่วน</h4>
        <p>เลือกการดำเนินการ</p>
      </div>
      <div class="trading-actions-buttons">
        <!-- Buy Order Form -->
        <form action="/markets/buy" method="POST" style="flex: 1;">
          <input type="hidden" name="lang" value="{{ .lang }}">
          <input type="hidden" name="product_id" id="buyProductId" value="">
          <button type="submit" class="trading-action-btn buy-order-btn">
            <span class="btn-icon">📈</span>
            <span class="btn-text">ซื้อ</span>
          </button>
        </form>

        <!-- Sell Order Form -->
        <form action="/markets/sell" method="POST" style="flex: 1;">
          <input type="hidden" name="lang" value="{{ .lang }}">
          <input type="hidden" name="product_id" id="sellProductId" value="">
          <button type="submit" class="trading-action-btn sell-order-btn">
            <span class="btn-icon">📉</span>
            <span class="btn-text">ขาย</span>
          </button>
        </form>
      </div>
    </div>
  </div>

  <div class="analytics-card">
    <h3>ราคาปิดตลาด</h3>
    <h5>กำลังโหลดวันที่...</h5>

    <div class="time-filter-container">
      <button class="time-filter-btn active" data-days="1">1 วัน</button>
      <button class="time-filter-btn" data-days="7">1 สัปดาห์</button>
      <button class="time-filter-btn" data-days="30">1 เดือน</button>
      <button class="time-filter-btn" data-days="365">1 ปี</button>
    </div>

    <div class="futures-table-wrapper">
      <table class="futures-table">
        <thead>
          <tr>
            <th>ระยะเวลาสัญญา</th>
            <th>เปิด</th>
            <th>สูงสุด</th>
            <th>ต่ำสุด</th>
            <th>ปริมาณเสนอซื้อ</th>
            <th>ราคาเสนอซื้อ</th>
            <th>เปลี่ยนแปลง</th>
            <th>ส่วนต่าง</th>
            <th>เปิด</th>
            <th>สูงสุด</th>
            <th>ต่ำสุด</th>
            <th>ปริมาณเสนอขาย</th>
            <th>ราคาเสนอขาย</th>
            <th>เปลี่ยนแปลง</th>
            <th>ส่วนต่าง</th>
          </tr>
        </thead>
        <tbody>
          <tr>

          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="analytics-card">
    <div class="chart-header">
      <h3>การวิเคราะห์ประวัติราคา</h3>
      <div class="live-indicator" id="liveIndicator" style="display: none;">
        <span class="live-dot"></span>
        <span class="live-text">อัปเดตสด</span>
      </div>
    </div>
    <div class="chart-container">
      <canvas id="priceHistoryChart"></canvas>
      <div id="priceHistoryLoading" class="chart-loading" style="display: none;">
        <div class="spinner"></div>
        <p>กำลังโหลดข้อมูลราคา...</p>
      </div>
      <div id="priceHistoryNoData" class="chart-no-data" style="display: none;">
        <p>ไม่มีข้อมูลประวัติราคาสำหรับเงื่อนไขที่เลือก</p>
      </div>
      <div id="updateIndicator" class="update-indicator" style="display: none;">
        <span class="update-text">กำลังอัปเดต...</span>
      </div>
    </div>
    <div class="time-filter">
      <button class="time-btn active" data-days="1">1D</button>
      <button class="time-btn" data-days="7">1W</button>
      <button class="time-btn" data-days="30">1M</button>
      <button class="time-btn" data-days="365">1Y</button>
    </div>
  </div>

</main>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .lang }}">คำถามพบบ่อย</a> | <a
    href="/contact?lang={{ .lang }}">ติดต่อเรา</a><br>
  <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
  <br>
</footer>
{{ end }}